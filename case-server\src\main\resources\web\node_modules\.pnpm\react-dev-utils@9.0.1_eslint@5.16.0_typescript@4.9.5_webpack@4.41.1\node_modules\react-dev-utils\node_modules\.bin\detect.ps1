#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\detect-port-alt@1.1.6\node_modules\detect-port-alt\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\detect-port-alt@1.1.6\node_modules\detect-port-alt\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\detect-port-alt@1.1.6\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/detect-port-alt@1.1.6/node_modules/detect-port-alt/bin/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/detect-port-alt@1.1.6/node_modules/detect-port-alt/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/detect-port-alt@1.1.6/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../../../../../detect-port-alt@1.1.6/node_modules/detect-port-alt/bin/detect-port" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../../../../../detect-port-alt@1.1.6/node_modules/detect-port-alt/bin/detect-port" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../../../../../detect-port-alt@1.1.6/node_modules/detect-port-alt/bin/detect-port" $args
  } else {
    & "node$exe"  "$basedir/../../../../../detect-port-alt@1.1.6/node_modules/detect-port-alt/bin/detect-port" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
