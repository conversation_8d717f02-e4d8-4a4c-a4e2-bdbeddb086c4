@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\webpack@4.41.1\node_modules\webpack\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\webpack@4.41.1\node_modules\webpack\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\webpack@4.41.1\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\webpack@4.41.1\node_modules\webpack\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\webpack@4.41.1\node_modules\webpack\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\webpack@4.41.1\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\webpack@4.41.1\node_modules\webpack\bin\webpack.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\webpack@4.41.1\node_modules\webpack\bin\webpack.js" %*
)
