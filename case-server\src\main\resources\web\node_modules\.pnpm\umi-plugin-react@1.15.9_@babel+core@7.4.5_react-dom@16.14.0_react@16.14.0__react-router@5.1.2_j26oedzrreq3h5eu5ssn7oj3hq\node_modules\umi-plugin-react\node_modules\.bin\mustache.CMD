@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\mustache@3.0.1\node_modules\mustache\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\mustache@3.0.1\node_modules\mustache\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\mustache@3.0.1\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\mustache@3.0.1\node_modules\mustache\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\mustache@3.0.1\node_modules\mustache\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\mustache@3.0.1\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\mustache@3.0.1\node_modules\mustache\bin\mustache" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\mustache@3.0.1\node_modules\mustache\bin\mustache" %*
)
