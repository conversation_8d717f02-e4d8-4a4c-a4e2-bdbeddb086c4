{"packageManager": "pnpm@9.6.0", "scripts": {"start": "cross-env NODE_OPTIONS='--openssl-legacy-provider' umi dev", "build": "cross-env NODE_OPTIONS='--openssl-legacy-provider' umi build", "test": "umi test", "lint": "eslint --ext .js src mock tests", "precommit": "lint-staged"}, "dependencies": {"antd": "^3.23.6", "axios": "^0.19.0", "dns": "^0.2.2", "dva": "2.6.0-beta.6", "enquire-js": "^0.2.1", "fast-json-patch": "^3.0.0-1", "for-editor": "^0.3.5", "hotbox-ui": "^1.0.0", "http2": "^3.3.7", "kity": "^2.0.4", "lodash.debounce": "^4.0.8", "marked": "^1.1.1", "module": "^1.2.5", "moment": "^2.24.0", "prop-types": "^15.7.2", "qrcode.react": "^1.0.0", "rc-queue-anim": "^1.8.5", "rc-scroll-anim": "^2.7.4", "rc-texty": "^0.2.0", "rc-tween-one": "^3.0.6", "react": "^16.8.6", "react-color": "^2.18.1", "react-dom": "^16.8.6", "react-resize-panel": "^0.3.5", "react-websocket": "^2.1.0", "readline": "^1.3.0", "repl": "^0.1.3", "webdriverio": "7.19.5"}, "devDependencies": {"babel-eslint": "^9.0.0", "cross-env": "^7.0.3", "eslint": "^5.4.0", "eslint-config-prettier": "^6.1.0", "eslint-config-standard": "^11.0.0", "eslint-config-umi": "^1.4.0", "eslint-plugin-flowtype": "^2.50.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-node": "^6.0.1", "eslint-plugin-prettier": "^3.1.0", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-react": "^7.14.3", "eslint-plugin-standard": "^3.1.0", "husky": "^0.14.3", "lint-staged": "^9.2.5", "prettier": "^1.18.2", "react-test-renderer": "^16.7.0", "sass": "^1.89.2", "sass-loader": "^7.3.1", "umi": "2.13.15", "umi-plugin-react": "^1.8.4"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx}": ["eslint --fix", "git add"]}, "engines": {"node": ">=8.0.0"}, "eslintIgnore": ["dist/"]}