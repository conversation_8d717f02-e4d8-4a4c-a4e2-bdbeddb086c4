@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\typescript@4.9.5\node_modules\typescript\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\typescript@4.9.5\node_modules\typescript\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\typescript@4.9.5\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\typescript@4.9.5\node_modules\typescript\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\typescript@4.9.5\node_modules\typescript\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\typescript@4.9.5\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\typescript@4.9.5\node_modules\typescript\bin\tsserver" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\typescript@4.9.5\node_modules\typescript\bin\tsserver" %*
)
