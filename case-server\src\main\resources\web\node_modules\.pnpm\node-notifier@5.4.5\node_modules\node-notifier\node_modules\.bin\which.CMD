@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\which@1.3.1\node_modules\which\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\which@1.3.1\node_modules\which\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\which@1.3.1\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\which@1.3.1\node_modules\which\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\which@1.3.1\node_modules\which\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\which@1.3.1\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\which@1.3.1\node_modules\which\bin\which" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\which@1.3.1\node_modules\which\bin\which" %*
)
