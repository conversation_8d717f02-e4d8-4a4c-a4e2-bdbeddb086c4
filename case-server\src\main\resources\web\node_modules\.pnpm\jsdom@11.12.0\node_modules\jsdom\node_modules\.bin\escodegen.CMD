@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\escodegen@1.14.3\node_modules\escodegen\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\escodegen@1.14.3\node_modules\escodegen\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\escodegen@1.14.3\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\escodegen@1.14.3\node_modules\escodegen\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\escodegen@1.14.3\node_modules\escodegen\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\escodegen@1.14.3\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\escodegen@1.14.3\node_modules\escodegen\bin\escodegen.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\escodegen@1.14.3\node_modules\escodegen\bin\escodegen.js" %*
)
