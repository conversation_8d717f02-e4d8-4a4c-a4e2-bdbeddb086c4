#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/umi-webpack-bundle-analyzer@3.6.2/node_modules/umi-webpack-bundle-analyzer/lib/bin/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/umi-webpack-bundle-analyzer@3.6.2/node_modules/umi-webpack-bundle-analyzer/lib/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/umi-webpack-bundle-analyzer@3.6.2/node_modules/umi-webpack-bundle-analyzer/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/umi-webpack-bundle-analyzer@3.6.2/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/umi-webpack-bundle-analyzer@3.6.2/node_modules/umi-webpack-bundle-analyzer/lib/bin/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/umi-webpack-bundle-analyzer@3.6.2/node_modules/umi-webpack-bundle-analyzer/lib/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/umi-webpack-bundle-analyzer@3.6.2/node_modules/umi-webpack-bundle-analyzer/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/umi-webpack-bundle-analyzer@3.6.2/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../umi-webpack-bundle-analyzer@3.6.2/node_modules/umi-webpack-bundle-analyzer/lib/bin/analyzer.js" "$@"
else
  exec node  "$basedir/../../../../../umi-webpack-bundle-analyzer@3.6.2/node_modules/umi-webpack-bundle-analyzer/lib/bin/analyzer.js" "$@"
fi
