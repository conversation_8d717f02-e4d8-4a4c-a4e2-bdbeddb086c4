@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\umi-webpack-bundle-analyzer@3.6.2\node_modules\umi-webpack-bundle-analyzer\lib\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\umi-webpack-bundle-analyzer@3.6.2\node_modules\umi-webpack-bundle-analyzer\lib\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\umi-webpack-bundle-analyzer@3.6.2\node_modules\umi-webpack-bundle-analyzer\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\umi-webpack-bundle-analyzer@3.6.2\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\umi-webpack-bundle-analyzer@3.6.2\node_modules\umi-webpack-bundle-analyzer\lib\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\umi-webpack-bundle-analyzer@3.6.2\node_modules\umi-webpack-bundle-analyzer\lib\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\umi-webpack-bundle-analyzer@3.6.2\node_modules\umi-webpack-bundle-analyzer\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\umi-webpack-bundle-analyzer@3.6.2\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\umi-webpack-bundle-analyzer@3.6.2\node_modules\umi-webpack-bundle-analyzer\lib\bin\analyzer.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\umi-webpack-bundle-analyzer@3.6.2\node_modules\umi-webpack-bundle-analyzer\lib\bin\analyzer.js" %*
)
