<?xml version="1.0" encoding="UTF-8"?>
<configuration >
    <Properties>
        <Property name="baseDir">$${env:LOG_DIR:-logs}/</Property>
        <!--<Property name="baseDir">logs/case-server</Property>-->
        <Property name="PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread][%-5level][%l]:%m%n</Property>
    </Properties>
    <appenders>
        <!--这个输出控制台的配置 -->
        <Console name="STDOUT" target="SYSTEM_OUT">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch） -->
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="[%d{HH:mm:ss:SSS}] [%p] - %l - %m%n"/>
        </Console>

        <RollingRandomAccessFile name="FILE-INFO"
                                 fileName="${baseDir}/case-server-info.log"
                                 filePattern="${baseDir}/case-server-info.log.%d{yyyy-MM-dd}">
            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${PATTERN}"  charset="UTF-8"/>
            <TimeBasedTriggeringPolicy interval="1"/>
            <DefaultRolloverStrategy max="1">
                <Delete basePath="${baseDir}" maxDepth="2">
                    <IfFileName glob="*case-server-info.*.log"/>
                    <IfLastModified age="3d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="FILE-ERROR"
                                 fileName="${baseDir}/case-server-error.log"
                                 filePattern="${baseDir}/case-server-error.log.%d{yyyy-MM-dd}">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${PATTERN}" charset="UTF-8"/>
            <TimeBasedTriggeringPolicy interval="1"/>
            <DefaultRolloverStrategy max="1">
                <Delete basePath="${baseDir}" maxDepth="2">
                    <IfFileName glob="*case-server-error.*.log"/>
                    <IfLastModified age="3d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

    </appenders>

    <!-- 全局配置，默认所有的Logger都继承此配置 -->
    <loggers>
        <Logger name="org.springframework" level="INFO"></Logger>
        <logger name="org.mybatis" level="INFO"></logger>
        <AsyncRoot level="all" includeLocation="true">
            <AppenderRef ref="FILE-INFO"/>
            <AppenderRef ref="FILE-ERROR"/>
            <AppenderRef ref="STDOUT"/>
        </AsyncRoot>
    </loggers>

</configuration>
