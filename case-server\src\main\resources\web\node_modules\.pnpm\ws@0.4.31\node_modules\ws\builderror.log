gyp info it worked if it ends with ok
gyp info using node-gyp@10.1.0
gyp info using node@20.11.1 | win32 | x64
gyp info find Python using Python version 3.12.4 found at "D:\python\python.exe"
gyp http GET https://nodejs.org/download/release/v20.11.1/node-v20.11.1-headers.tar.gz
gyp http 200 https://nodejs.org/download/release/v20.11.1/node-v20.11.1-headers.tar.gz
gyp http GET https://nodejs.org/download/release/v20.11.1/SHASUMS256.txt
gyp http GET https://nodejs.org/download/release/v20.11.1/win-x64/node.lib
gyp http 200 https://nodejs.org/download/release/v20.11.1/SHASUMS256.txt
gyp http 200 https://nodejs.org/download/release/v20.11.1/win-x64/node.lib
gyp ERR! find VS 
gyp ERR! find VS msvs_version not set from command line or npm config
gyp ERR! find VS VCINSTALLDIR not set, not running in VS Command Prompt
gyp ERR! find VS could not use PowerShell to find Visual Studio 2017 or newer, try re-running with '--loglevel silly' for more details.
gyp ERR! find VS 
gyp ERR! find VS Failure details: undefined
gyp ERR! find VS could not use PowerShell to find Visual Studio 2017 or newer, try re-running with '--loglevel silly' for more details.
gyp ERR! find VS 
gyp ERR! find VS Failure details: undefined
gyp ERR! find VS could not use PowerShell to find Visual Studio 2017 or newer, try re-running with '--loglevel silly' for more details.
gyp ERR! find VS 
gyp ERR! find VS Failure details: undefined
gyp ERR! find VS could not use PowerShell to find Visual Studio 2017 or newer, try re-running with '--loglevel silly' for more details.
gyp ERR! find VS 
gyp ERR! find VS Failure details: undefined
gyp ERR! find VS not looking for VS2015 as it is only supported up to Node.js 18
gyp ERR! find VS not looking for VS2013 as it is only supported up to Node.js 8
gyp ERR! find VS 
gyp ERR! find VS **************************************************************
gyp ERR! find VS You need to install the latest version of Visual Studio
gyp ERR! find VS including the "Desktop development with C++" workload.
gyp ERR! find VS For more information consult the documentation at:
gyp ERR! find VS https://github.com/nodejs/node-gyp#on-windows
gyp ERR! find VS **************************************************************
gyp ERR! find VS 
gyp ERR! configure error 
gyp ERR! stack Error: Could not find any Visual Studio installation to use
gyp ERR! stack at VisualStudioFinder.fail (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pnpm\dist\node_modules\node-gyp\lib\find-visualstudio.js:116:11)
gyp ERR! stack at VisualStudioFinder.findVisualStudio (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pnpm\dist\node_modules\node-gyp\lib\find-visualstudio.js:72:17)
gyp ERR! stack at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
gyp ERR! stack at async createBuildDir (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pnpm\dist\node_modules\node-gyp\lib\configure.js:95:26)
gyp ERR! stack at async run (C:\Users\<USER>\AppData\Roaming\npm\node_modules\pnpm\dist\node_modules\node-gyp\bin\node-gyp.js:81:18)
gyp ERR! System Windows_NT 10.0.19044
gyp ERR! command "C:\\nvm4w\\nodejs\\node.exe" "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\pnpm\\dist\\node_modules\\node-gyp\\bin\\node-gyp.js" "rebuild"
gyp ERR! cwd D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\ws@0.4.31\node_modules\ws
gyp ERR! node -v v20.11.1
gyp ERR! node-gyp -v v10.1.0
gyp ERR! not ok 
