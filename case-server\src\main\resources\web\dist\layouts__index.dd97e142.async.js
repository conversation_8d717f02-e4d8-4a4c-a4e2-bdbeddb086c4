(window.webpackJsonp=window.webpackJsonp||[]).push([[1],{aArQ:function(e,t,n){"use strict";n.r(t);n("Llx4");var a=n("LPzL"),l=(n("wEGQ"),n("CM+a")),r=n("nNWW"),o=n.n(r),c=n("dbwt"),u=n.n(c),i=n("9PTj"),d=n("Exlg"),s=l.a.Content;t.default=u()(Object(d.c)(function(e){return{global:e.global}})(class extends r.Component{render(){var e=this.props.children,t=void 0===e?{}:e;return o.a.createElement(a.a,{locale:i.a},o.a.createElement(l.a,null,o.a.createElement(s,{style:{minHeight:"100vh"}},t)))}}))},dbwt:function(e,t,n){e.exports=n("zqL4").default},zqL4:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n("wW6N").withRouter;t.default=a}}]);