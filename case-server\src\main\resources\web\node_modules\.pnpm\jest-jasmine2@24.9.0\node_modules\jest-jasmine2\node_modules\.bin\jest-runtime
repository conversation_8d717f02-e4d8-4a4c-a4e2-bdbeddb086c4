#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/jest-runtime@24.9.0/node_modules/jest-runtime/bin/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/jest-runtime@24.9.0/node_modules/jest-runtime/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/jest-runtime@24.9.0/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/jest-runtime@24.9.0/node_modules/jest-runtime/bin/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/jest-runtime@24.9.0/node_modules/jest-runtime/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/jest-runtime@24.9.0/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../jest-runtime@24.9.0/node_modules/jest-runtime/bin/jest-runtime.js" "$@"
else
  exec node  "$basedir/../../../../../jest-runtime@24.9.0/node_modules/jest-runtime/bin/jest-runtime.js" "$@"
fi
