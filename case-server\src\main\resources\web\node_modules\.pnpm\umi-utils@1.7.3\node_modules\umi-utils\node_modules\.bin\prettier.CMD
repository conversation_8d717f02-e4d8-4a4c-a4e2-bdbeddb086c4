@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\prettier@1.15.3\node_modules\prettier\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\prettier@1.15.3\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\prettier@1.15.3\node_modules\prettier\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\prettier@1.15.3\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\prettier@1.15.3\node_modules\prettier\bin-prettier.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\prettier@1.15.3\node_modules\prettier\bin-prettier.js" %*
)
