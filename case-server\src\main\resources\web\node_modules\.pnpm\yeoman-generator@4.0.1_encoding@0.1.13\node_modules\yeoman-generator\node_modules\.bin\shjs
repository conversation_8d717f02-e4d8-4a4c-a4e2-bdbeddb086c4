#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/shelljs@0.8.5/node_modules/shelljs/bin/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/shelljs@0.8.5/node_modules/shelljs/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/shelljs@0.8.5/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/shelljs@0.8.5/node_modules/shelljs/bin/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/shelljs@0.8.5/node_modules/shelljs/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/shelljs@0.8.5/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../shelljs@0.8.5/node_modules/shelljs/bin/shjs" "$@"
else
  exec node  "$basedir/../../../../../shelljs@0.8.5/node_modules/shelljs/bin/shjs" "$@"
fi
