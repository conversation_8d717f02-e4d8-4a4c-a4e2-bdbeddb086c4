import { But<PERSON>, Dropdown, Icon, <PERSON>u, Select } from 'antd'
import React from 'react'

class FilterLabel extends React.Component {
  constructor(props) {
    super(props)
    this.state = props.searchGroup
  }

  componentDidUpdate(prevProps, prevState) {
    // state改变时，更新props,给组件传入最新的this.state
    if (
      prevState.searchParams !== this.state.searchParams ||
      prevState.searchGroups !== this.state.searchGroups
    ) {
      this.props.emitState(this.state)
    }
    // props改变时，更新state
    if (prevProps.searchGroup !== this.props.searchGroup) {
      this.setState(this.props.searchGroup)
    }
  }
  handleFilterChange = (index, field, value) => {
    const newSearchParams = [...this.state.searchParams]
    newSearchParams[index][field] = value
    if (field === 'paramKey') {
      newSearchParams[index].value = []
      newSearchParams[index].operator = ''
    }
    if (field === 'operator') {
      newSearchParams[index].value = []
    }
    this.setState({ searchParams: newSearchParams })
  }
  addFilter = () => {
    const newFilter = { operator: '', paramKey: '', value: [] }
  const currentParams = this.state.searchParams || []

  this.setState({
    searchParams: [...currentParams, newFilter],
  })
  }
  // 子组件更新state时，props函数获取最新的值更新父组件的state
  emitState = stateParams => {
    const newSearchGroups = this.state.searchGroups.map(group => {
      if (group.id === stateParams.id) {
        return stateParams
      }
      return group
    })
    this.setState({
      searchGroups: newSearchGroups,
    })
  }
  addGroupFilter = () => {
    this.setState({
      searchGroups: [
        ...this.state.searchGroups,
        {
          conjunction: 'AND',
          searchParams: [],
          searchGroups: [],
          id: Math.random() * 16 + new Date().getTime(),
        },
      ],
    })
  }
  removeFilter = index => {
    const newSearchParams = [...this.state.searchParams]
    newSearchParams.splice(index, 1)
    this.setState({ searchParams: newSearchParams })
  }
  handleConjunctionChange = () => {
    this.setState({
      conjunction: this.state.conjunction === 'AND' ? 'OR' : 'AND',
    })
  }
  removeGroupFilter = index => {
    const newSearchGroups = this.state.searchGroups ? [...this.state.searchGroups] : []
    newSearchGroups.splice(index, 1)
    this.setState({ searchGroups: newSearchGroups })
  }

  render() {
    const { Option } = Select
    const menu = (
      <Menu>
        <Menu.Item>
          <div onClick={this.addGroupFilter}>添加条件组</div>
        </Menu.Item>
      </Menu>
    )
    return (
      <div
        style={{
          border: '1px solid rgb(222, 224, 227)',
          padding: '10px',
          borderRadius: '8px',
        }}
      >
        <div style={{ width: '100%' }} className="filter-label">
          {this.state.searchParams?.length + this.state.searchGroups?.length > 1 ? (
            <div className="conjunction">
              <div className="conjunction-item-border conjunction-item-border-top" />
              <div className="conjunction-item" onClick={this.handleConjunctionChange}>
                {this.state.conjunction === 'AND' ? '且' : '或'}
              </div>
              <div className="conjunction-item-border conjunction-item-border-bottom" />
            </div>
          ) : null}
          <div style={{ flexGrow: 1 }}>
            {this.state.searchParams?.map((filter, index) => (
              <div
                key={index}
                style={{
                  display: 'flex',
                  marginBottom: 8,
                  alignItems: 'center',
                  gap: '3px',
                }}
              >
                {/* 选择字段 */}
                <Select
                  style={{ width: 140 }}
                  placeholder="选择字段"
                  value={filter.paramKey}
                  onChange={value => this.handleFilterChange(index, 'paramKey', value)}
                >
                  <Option value="priority">优先级</Option>
                  <Option value="resource">标签</Option>
                </Select>
                {/* 选择运算符 */}
                <Select
                  style={{ width: 240 }}
                  value={filter.operator}
                  onChange={value => this.handleFilterChange(index, 'operator', value)}
                  disabled={!filter.paramKey}
                >
                  <Option value="=">等于</Option>
                  <Option value="!=">不等于</Option>
                  <Option value="HAS ANY OF">存在选项属于</Option>
                  <Option value="HAS ALL OF">全部选项均不属于</Option>
                  {filter.paramKey === 'resource' ? <Option value="CONTAINS">包含</Option> : null}
                  {filter.paramKey === 'resource' ? (
                    <Option value="NOT CONTAINS">不包含</Option>
                  ) : null}
                </Select>
                {filter.paramKey === 'resource' ? (
                  <Select
                    placeholder="请选择标签"
                    onChange={value => this.handleFilterChange(index, 'value', value)}
                    value={filter.value}
                    mode="multiple"
                    disabled={!filter.paramKey || !filter.operator}
                  >
                    {this.props.caseInfo.taglist &&
                      this.props.caseInfo.taglist.length > 0 &&
                      this.props.caseInfo.taglist.map(item => (
                        <Option
                          disabled={
                            (filter.operator === '=' || filter.operator === '!=') &&
                            item &&
                            !item.includes(filter.value)
                          }
                          key={item}
                        >
                          {item}
                        </Option>
                      ))}
                  </Select>
                ) : (
                  <Select
                    placeholder="请选择优先级"
                    onChange={value => this.handleFilterChange(index, 'value', value)}
                    value={filter.value}
                    mode="multiple"
                    disabled={!filter.paramKey || !filter.operator}
                  >
                    {[0, 1, 2].map((item, index) => (
                      <Option
                        key={index}
                        value={item + 1} // 设置为数值类型
                        disabled={
                          (filter.operator === '=' || filter.operator === '!=') &&
                          !(item + 1).toString().includes(filter.value) // 对数值进行字符串处理
                        }
                      >
                        {`P${item}`}
                      </Option>
                    ))}
                  </Select>
                )}

                <Icon type="delete" onClick={() => this.removeFilter(index)} />
              </div>
            ))}
            {this.state.searchGroups?.map((group, index) => (
              <div
                key={group.id}
                style={{
                  marginBottom: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '3px',
                }}
              >
                <div style={{ width: '100%' }}>
                  <FilterLabel
                    caseInfo={this.props.caseInfo}
                    searchGroup={group}
                    emitState={this.emitState}
                  />
                </div>
                <Icon type="delete" onClick={() => this.removeGroupFilter(index)} />
              </div>
            ))}
          </div>
        </div>
        {/* 添加按钮 */}
        <div style={{ display: 'flex', gap: '3px', alignItems: 'center' }}>
          <Button onClick={this.addFilter}>添加条件</Button>

          {/* 添加按钮 */}

          <Dropdown overlay={menu} trigger={['click']}>
            <a className="ant-dropdown-link" onClick={e => e.preventDefault()}>
              <Icon type="caret-down" />
            </a>
          </Dropdown>
        </div>
      </div>
    )
  }
}

export default FilterLabel
