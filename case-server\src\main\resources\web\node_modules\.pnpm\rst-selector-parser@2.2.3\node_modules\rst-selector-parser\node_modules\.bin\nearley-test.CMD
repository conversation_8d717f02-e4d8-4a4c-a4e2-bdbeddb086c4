@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\nearley@2.20.1\node_modules\nearley\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\nearley@2.20.1\node_modules\nearley\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\nearley@2.20.1\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\nearley@2.20.1\node_modules\nearley\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\nearley@2.20.1\node_modules\nearley\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\nearley@2.20.1\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\nearley@2.20.1\node_modules\nearley\bin\nearley-test.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\nearley@2.20.1\node_modules\nearley\bin\nearley-test.js" %*
)
