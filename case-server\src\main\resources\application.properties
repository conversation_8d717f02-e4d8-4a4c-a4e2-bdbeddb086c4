# 默认启动环境
spring.profiles.active=dev
spring.application.name=case-server

# HTTPs端口
server.port=8443

# HTTPS配置
https.ssl.enable=true
https.ssl.key-store=classpath:keystore.p12
https.ssl.key-store-password=12345678
https.ssl.keyStoreType=PKCS12
https.ssl.keyAlias=tomcat

http.port=8080

# host在本地测试可以设置为localhost或者本机IP，在Linux服务器跑可换成服务器IP
socketio.host=localhost
socketio.port=8095
socketio.context=/custom_socket
# 设置最大每帧处理数据的长度，防止他人利用大数据来攻击服务器
socketio.maxFramePayloadLength=2097152
# 设置http交互最大内容长度
socketio.maxHttpContentLength=2097152
# socket连接数大小（如只监听一个端口boss线程组为1即可）
socketio.bossCount=1
socketio.workCount=100
socketio.allowCustomRequests=true
# 协议升级超时时间（毫秒），默认10秒。HTTP握手升级为ws协议超时时间
socketio.upgradeTimeout=1000000
# Ping消息超时时间（毫秒），默认60秒，这个时间间隔内没有接收到心跳消息就会发送超时事件
socketio.pingTimeout=6000000
# Ping消息间隔（毫秒），默认25秒。客户端向服务器发送一条心跳消息间隔
socketio.pingInterval=25000

# mybatis.xml文件位置配置
mybatis.typeAliasesPackage=com.xiaoju.framework.*
mybatis.mapperLocations=classpath*:mapper/*.xml

# 文件上传限制,后端改为了100M
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=101MB

spring.datasource.druid.connection-init-sqls=set names utf8mb4

# log配置
logging.config=classpath:log4j2.xml

# 解析项目下的前端包
spring.thymeleaf.servlet.content-type=text/html
spring.thymeleaf.encoding=utf-8
spring.thymeleaf.mode=LEGACYHTML5
spring.thymeleaf.cache=false
spring.mvc.static-path-pattern=/**
spring.resources.static-locations=classpath:/web/dist/
spring.thymeleaf.prefix=classpath:/web/dist/
spring.thymeleaf.suffix=.html

# 关闭devtools
spring.devtools.add-properties=false

# 权限开关，默认关闭
authority.flag=false

# 用户注册
user.register.enable= ${REGISTER_ENABLE:true}

# 用户本地鉴权
user.normal.enable= ${NORMAL_USER_ENABLE:true}

# 用户ldap鉴权
user.ldap.enable= ${LDAP_USER_ENABLE:true}
user.ldap.userId= ${LDAP_USER_ID:sAMAccountName}
user.ldap.userBaseDN= ${LDAP_BASE_DN:ou=Account_Users}
user.ldap.objectClass= ${LDAP_OBJECT_CLASS:person}

# LDAP搜索
spring.ldap.base=${LDAP_BASE:ou=system}
spring.ldap.username=${LDAP_USERNAME:cn=yapi,ou=\u516C\u5171,ou=Account_Users,dc=int,dc=hypergryph,dc=com}
spring.ldap.password=${LDAP_PASSWORD:AgileTC!@#123}
spring.ldap.urls=${LDAP_URL:ldap://127.0.0.1:10389}

# 权限开关，默认关闭
authority.flag=false