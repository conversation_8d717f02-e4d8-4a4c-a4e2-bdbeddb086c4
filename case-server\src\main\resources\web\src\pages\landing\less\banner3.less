@banner3: banner3;
.@{banner3} {
  // 如果在第一屏且导航位置为 relative, 一屏为 height: calc(~"100vh - 64px");
  width: 100%;
  height: calc(~'100vh - 64px');
  min-height: 640px;
  position: relative;
  text-align: center;
  border-color: #666;
  background: radial-gradient(
    circle at 50% 50%,
    rgba(6, 102, 219, 0.1) 0%,
    rgba(6, 102, 219, 0.1) 33.333%,
    rgba(6, 102, 219, 0.3) 33.333%,
    rgba(6, 102, 219, 0.3) 66.666%,
    rgba(6, 102, 219, 0.5) 66.666%,
    rgba(6, 102, 219, 0.5) 99.999%
  );
  // background-image: url('https://gw.alipayobjects.com/zos/rmsportal/xTPkCNNLOnTEbGgVZOpE.jpg');
  // background-size: cover;
  // background-attachment: fixed;
  background-position: center;
  padding: 120px 0px;
  & &-text-wrapper {
    // position: absolute;
    // top: 24px;
    margin: auto;
    // left: 0;
    // right: 0;
    color: #3c4858;
    // color: @template-text-color-light;
    max-width: 845px;
    height: 400px;
    width: 80%;
    font-size: 20px;
    line-height: 28px;
    font-weight: 400;
    > .queue-anim-leaving {
      position: relative !important;
    }
  }
  & &-slogan {
    font-size: 56px;
    line-height: 80px;
    text-indent: 2px;
    font-weight: 600;
    margin: 0px auto 38px;
    overflow: hidden;
  }
  & &-logo {
    width: 54px;
  }
  & &-name-en {
    display: block;
    font-size: 14px;
    line-height: 32px;
    font-weight: 400;
  }
  & &-name {
    font-size: 24px;
    overflow: hidden;
    opacity: 0.8;
  }
  & &-button {
    display: block;
    margin: 72px auto 0;
    background: #0666db;
    // background: rgb(3, 67, 101);
    // background: -moz-linear-gradient(
    //   left,
    //   rgba(3, 67, 101, 1) 0%,
    //   rgba(0, 27, 51, 1) 100%
    // );
    // background: linear-gradient(
    //   to right,
    //   rgba(3, 67, 101, 1) 0%,
    //   rgba(0, 27, 51, 1) 100%
    // );
    box-shadow: 0 8px 16px #0a52ab;
    border: none;
    transition: background 0.45s @ease-out;
    width: 132px;
    line-height: 42px;
    height: 42px;
    border-radius: 8px;
  }
  & &-time {
    font-size: 14px;
    line-height: 24px;
    margin-top: 24px;
  }
}

@media screen and (max-width: 767px) {
  .@{banner3} {
    background-attachment: inherit;
    padding: 200px 0px;
    & &-text-wrapper {
      width: 90%;
      height: 50%;
    }
    & &-name-en {
      font-size: 12px;
    }
    & &-slogan {
      font-size: 24px;
      line-height: 1.5;
      margin: 12px 0;
    }
    & &-name {
      font-size: 14px;
    }
  }
}
