#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/detect-port-alt@1.1.6/node_modules/detect-port-alt/bin/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/detect-port-alt@1.1.6/node_modules/detect-port-alt/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/detect-port-alt@1.1.6/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/detect-port-alt@1.1.6/node_modules/detect-port-alt/bin/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/detect-port-alt@1.1.6/node_modules/detect-port-alt/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/detect-port-alt@1.1.6/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../detect-port-alt@1.1.6/node_modules/detect-port-alt/bin/detect-port" "$@"
else
  exec node  "$basedir/../../../../../detect-port-alt@1.1.6/node_modules/detect-port-alt/bin/detect-port" "$@"
fi
