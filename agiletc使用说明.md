## agiletc使用说明

### 登陆注册

第一步：进入首部页面，如果没有注册过，则点击【注册】按钮，输入账号和密码进行登录

<img src="https://dpubstatic.udache.com/static/dpubimg/113ed6a2-143c-49ed-9bcd-d818f773e221.png" style="zoom:50%;" />

第二步：如果已经注册过，则点击【登陆】按钮，输入账号和密码进行登录

<img src="https://dpubstatic.udache.com/static/dpubimg/abaac832-3cb3-40e7-b9b9-ed98ec95a71c.png" style="zoom:50%;" />

### 新建用例

第一步：点击【新建用例集】按钮，弹出"新增测试用例集"弹窗

<img src="https://dpubstatic.udache.com/static/dpubimg/5eca11a9-83ca-43e6-9cf7-fc89f5b1bc58.png" style="zoom:50%;" />

第二步：输入用例集名称（必填），关联需求，用例集分类（必填），描述，导入本地xmind文件

<img src="https://dpubstatic.udache.com/static/dpubimg/004b82f0-2932-4a73-9388-316973a78fff.png" style="zoom:50%;" />

第三步：点击"确定"按钮，新的测试用例就新增成功啦

第四步：点击"用例名称"，跳转到用例详情页，展示刚刚新增的具体用例内容，如图

<img src="https://dpubstatic.udache.com/static/dpubimg/a5007a80-2603-4638-9e1f-7bca0c316a93.png" style="zoom:50%;" />

<img src="https://dpubstatic.udache.com/static/dpubimg/8673b7c1-34fc-4803-8cc1-730915871937.png" style="zoom:50%;" />

### 编辑用例

第一步：点击【编辑用例】按钮，弹出"修改测试用例"弹窗，可更改用例集名称，关联需求，用例集分类及描述，更改完成之后点击“确定”按钮

<img src="https://dpubstatic.udache.com/static/dpubimg/0f601abb-8564-4bc9-8b39-94901ff7bd0e.png" alt=" " style="zoom:50%;" />

第二步：更改完成之后，可在列表页看到最新更新人，如图

<img src="https://dpubstatic.udache.com/static/dpubimg/8a7ee595-acd7-4aeb-91f9-ee9436573d40.png" style="zoom:50%;" />

第三步：编辑用例内容，可点击"用例集名称"，跳转到用例详情页，增加或删选用例内容

第四步：添加一个同级主题时可以点击插入同级主题，也可以使用快捷键Enter，添加一个下级主题时可以点击插入下级主题，也可以使用快捷键Tab，添加一个上级主题时可以点击插入上级主题，如图

<img src="https://dpubstatic.udache.com/static/dpubimg/dbf10a68-9af1-4fab-b199-31c04c829594.png" style="zoom:50%;" />

第五步：可通过打tag的方式标记测试用例，选中标记的用例，添加所需要的tag，比如输入"预置条件"，点击"添加"按钮，"预置条件"tag被添加在选项框中，用例被标记"预置条件"tag，如已有tag，选中用例，勾选tag即可，取消掉勾选的tag，会取消用例勾选的tag，如图

<img src="https://dpubstatic.udache.com/static/dpubimg/d80ced72-7b6f-4805-8de8-9362093826dc.png" style="zoom:50%;" />

<img src="https://dpubstatic.udache.com/static/dpubimg/67487697-fc7e-4b50-9268-bdfd90b74c1f.png" style="zoom:50%;" />

第六步：可通过打优先级的方式标记测试用例，选中标记的用例，添加所需要的优先级，比如对AgileTC这个测试用例进行打优先级，点击测试用例，然后选择相对应的【优先级】P0，P1，P2，“优先级”被标记到测试用例，如果想取消标记的优先级，勾选“—”即可，会取消用例勾选的优先级，如图

<img src="https://dpubstatic.udache.com/static/dpubimg/5765ac0c-0d1e-473a-a854-1c6e0c8f73bb.png" style="zoom:50%;" />

<img src="https://dpubstatic.udache.com/static/dpubimg/ebe8a809-f7aa-427b-9819-483378a68589.png" style="zoom:50%;" />

第七步：可以给测试用例添加【链接】，添加【图片】以及添加【备注】

<img src="https://dpubstatic.udache.com/static/dpubimg/04edf805-c47e-4c85-9c0b-1e41c0c2d6dc.png" style="zoom:50%;" />

<img src="https://dpubstatic.udache.com/static/dpubimg/60285bb1-674f-4c04-a372-8fa79f79b26e.png" style="zoom:50%;" />

<img src="https://dpubstatic.udache.com/static/dpubimg/b646bf0d-b19d-4a5b-b582-9d0ea0413115.png" style="zoom:50%;" />

第八步：右键点击测试用例可以出现上面讲到的一些操作，包括插入上级主题，插入同级主题，插入下级主题，将测试用例前移，将测试用例后移，删除测试用例，编辑测试用例等操作，此外还可以定义优先级，如图

<img src="https://dpubstatic.udache.com/static/dpubimg/f9deea90-3407-4835-bcc4-85abdc70c64d.png" style="zoom:50%;" />

<img src="https://dpubstatic.udache.com/static/dpubimg/61556ce1-2e1c-43b3-97c1-0cef1577b408.png" style="zoom:50%;" />

第九步：点击【外观】可以更改背景主题，脑图类型，以及自动整理布局，如图

<img src="https://dpubstatic.udache.com/static/dpubimg/448e744d-857b-4ee0-b009-553388e8dcaa.png" style="zoom:50%;" />

<img src="https://dpubstatic.udache.com/static/dpubimg/40300122-418e-45ef-a494-4f47d932db8f.png" style="zoom:50%;" />

<img src="https://dpubstatic.udache.com/static/dpubimg/0145a9ac-2c3c-45c1-9cbf-0ab5f13ee1ff.png" style="zoom:50%;" />

第十步：点击【视图】可以选择想要看到的脑图结构

<img src="https://dpubstatic.udache.com/static/dpubimg/305123e7-5fab-4c87-999d-86b03e9278e8.png" style="zoom:50%;" />

第十一步：点击【用例锁】，此时用例被锁住，当前只能进行读，不能进行修改

<img src="https://dpubstatic.udache.com/static/dpubimg/96091081-88d0-4fbd-8b84-3b5d97732f2c.png" style="zoom:50%;" />

第十二步：点击【全屏】可以将脑图全屏展示

<img src="https://dpubstatic.udache.com/static/dpubimg/41ba4d61-198a-482b-b7f8-03d0f231b05c.png" style="zoom:50%;" />

### 执行用例

第一步：新建测试任务

（1）选择待执行的用例，点击"创建测试任务"按钮，弹出"新增测试任务"弹窗

<img src="https://dpubstatic.udache.com/static/dpubimg/05c88de0-2514-4b77-ac72-d2cb5d7c42c9.png" style="zoom:50%;" />

<img src="https://dpubstatic.udache.com/static/dpubimg/79de8939-a232-4be2-9b0d-078554185715.png" style="zoom:50%;" />

（2）输入名称（必填），负责人（非必填），描述（非必填），计划周期（非必填）

（3）选择用例

- 选择全部用例，测试任务便包含该测试用例全部用例

- 也可手动圈选部分用例，比如P0，P1，P2，测试任务便包含所选优先级的用例

  <img src="https://dpubstatic.udache.com/static/dpubimg/8a51b763-0a8f-4490-8aab-c5e012b9270b.png" style="zoom:50%;" />

（4）点击"确定"按钮，测试任务就新建成功啦

<img src="https://dpubstatic.udache.com/static/dpubimg/366eacd4-1ace-4f8a-9bc2-8ab76dadf29e.png" style="zoom:50%;" />

第二步：执行测试任务

（1） 点击用例id旁边的打开按钮，展示新建的测试任务list

<img src="https://dpubstatic.udache.com/static/dpubimg/eb143692-9374-462a-8695-808264839124.png" style="zoom:50%;" />

（2）点击"执行测试"按钮，跳转到新建任务详情页，就可以看到新建任务具体的测试用例

<img src="https://dpubstatic.udache.com/static/dpubimg/1b92babe-6070-4886-8714-3722ed7a6be2.png" style="zoom:50%;" />

（3）执行完一条用例，用例测试通过，点击对应的用例，比如"Authority"，再点击绿色的对号按钮，可以标记通过，如图

<img src="https://dpubstatic.udache.com/static/dpubimg/24f1170b-9638-4144-9ef3-d59929c34cb1.png" style="zoom:50%;" />

（4）执行完一条用例，用例测试不通过，点击对应的用例，比如"删除"，再点击黄色的"X"按钮，可以标记不通过，如图

<img src="https://dpubstatic.udache.com/static/dpubimg/2c00b301-0c52-4414-9d4e-8b702c7aa6c8.png" style="zoom:50%;" />

（5）当然也可以清除标记记录，清除对应的一条记录，点击"移除结果"按钮，标记的成功或失败记录即可消除，比如消除"删除"用例的不通过标记，如图

<img src="https://dpubstatic.udache.com/static/dpubimg/bc9f6e72-0c0e-42f7-a52a-c1b502847a8a.png" style="zoom:50%;" />

（6）需要清除测试任务所有用例的标记记录，可点击"清除执行记录"按钮，便可清除该任务所有用例的标记，再点击"保存"按钮即可

<img src="https://dpubstatic.udache.com/static/dpubimg/0eb52b2c-a2a1-47fd-a7e9-a0babd4aaea0.png" style="zoom:50%;" />

（7）执行用例之后，在任务详情页面可以看到执行用例的个数以及通过率，如图

<img src="https://dpubstatic.udache.com/static/dpubimg/7e545c85-ec8b-446c-a0a6-98e59267967c.png" style="zoom:50%;" />

（8）在任务列表页，可以看到这个测试任务的执行人以及通过率等信息，通过率表示已测用例集中通过的用例占总用例的百分比，如图

<img src="https://dpubstatic.udache.com/static/dpubimg/a1d6dc51-8b36-45a3-98e0-d8f22f3a6e6f.png" style="zoom:50%;" />

### 复制用例

第一步：选择待复制的用例，点击【复制测试集】按钮，弹出"复制测试用例"弹窗

<img src="https://dpubstatic.udache.com/static/dpubimg/2e9a4da4-f548-4cbe-814b-e384312b5d72.png" style="zoom:50%;" />

<img src="https://dpubstatic.udache.com/static/dpubimg/a571685b-1c1c-4042-bfc8-79e5daae8e7c.png" style="zoom:50%;" />

第二步：默认读取被复制用例的描述、用例集分类及用例内容，可更改用例名称、用例集分类和描述，可选择关联需求，点击"确认"按钮，提示"复制测试用例成功"，在列表页即可看到复制的用例

<img src="https://dpubstatic.udache.com/static/dpubimg/a571685b-1c1c-4042-bfc8-79e5daae8e7c.png" style="zoom:50%;" />

<img src="https://dpubstatic.udache.com/static/dpubimg/7ed09680-de15-41b8-af30-950f8155732b.png" style="zoom:50%;" />

### 删除用例

第一步：选择待删除的用例，点击【删除用例】按钮，弹出"确认删除用例"弹窗

<img src="https://dpubstatic.udache.com/static/dpubimg/7ed09680-de15-41b8-af30-950f8155732b.png" style="zoom:50%;" />

第二步：勾选"我明白以上操作"，点击【删除】按钮，可删除用例，用例及用例下的测试任务会全部删除，**不可恢复**

<img src="https://dpubstatic.udache.com/static/dpubimg/e01e0228-42ae-47e0-92fd-eedb430801a7.png" style="zoom:50%;" />

### 导出xmind文件

支持导出测试用例的xmind文件，在本地编辑，点击【导出xmind文件】按钮，可下载该xmind文件，如图

<img src="https://dpubstatic.udache.com/static/dpubimg/b10660fc-64b2-4552-879f-f38d74f0be58.png" style="zoom:50%;" />

#### 