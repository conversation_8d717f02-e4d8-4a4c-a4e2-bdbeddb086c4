@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\marked@0.6.2\node_modules\marked\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\marked@0.6.2\node_modules\marked\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\marked@0.6.2\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\marked@0.6.2\node_modules\marked\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\marked@0.6.2\node_modules\marked\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\marked@0.6.2\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\marked@0.6.2\node_modules\marked\bin\marked" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\marked@0.6.2\node_modules\marked\bin\marked" %*
)
