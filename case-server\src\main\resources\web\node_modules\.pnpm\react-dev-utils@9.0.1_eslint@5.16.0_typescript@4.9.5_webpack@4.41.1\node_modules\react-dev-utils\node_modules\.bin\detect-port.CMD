@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\detect-port-alt@1.1.6\node_modules\detect-port-alt\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\detect-port-alt@1.1.6\node_modules\detect-port-alt\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\detect-port-alt@1.1.6\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\detect-port-alt@1.1.6\node_modules\detect-port-alt\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\detect-port-alt@1.1.6\node_modules\detect-port-alt\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\detect-port-alt@1.1.6\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\detect-port-alt@1.1.6\node_modules\detect-port-alt\bin\detect-port" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\detect-port-alt@1.1.6\node_modules\detect-port-alt\bin\detect-port" %*
)
