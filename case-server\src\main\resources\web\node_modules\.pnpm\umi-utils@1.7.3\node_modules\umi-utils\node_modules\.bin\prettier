#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/prettier@1.15.3/node_modules/prettier/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/prettier@1.15.3/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/prettier@1.15.3/node_modules/prettier/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/prettier@1.15.3/node_modules:/mnt/d/projects/AgileTC/case-server/src/main/resources/web/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../prettier@1.15.3/node_modules/prettier/bin-prettier.js" "$@"
else
  exec node  "$basedir/../../../../../prettier@1.15.3/node_modules/prettier/bin-prettier.js" "$@"
fi
