@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\typescript@3.9.10\node_modules\typescript\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\typescript@3.9.10\node_modules\typescript\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\typescript@3.9.10\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\typescript@3.9.10\node_modules\typescript\bin\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\typescript@3.9.10\node_modules\typescript\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\typescript@3.9.10\node_modules;D:\projects\AgileTC\case-server\src\main\resources\web\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\typescript@3.9.10\node_modules\typescript\bin\tsc" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\typescript@3.9.10\node_modules\typescript\bin\tsc" %*
)
